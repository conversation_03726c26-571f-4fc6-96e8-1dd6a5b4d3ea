import React from 'react';
import StatsCard from '../components/Dashboard/StatsCard';
import {
  Users,
  ShoppingCart,
  Package,
  DollarSign,
  TrendingUp,
  Activity
} from 'lucide-react';

const Dashboard = () => {
  const stats = [
    {
      title: 'Tổng người dùng',
      value: '2,543',
      change: '+12% so với tháng trước',
      changeType: 'increase',
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Đơn hàng',
      value: '1,234',
      change: '+8% so với tháng trước',
      changeType: 'increase',
      icon: ShoppingCart,
      color: 'green'
    },
    {
      title: 'Sản phẩm',
      value: '856',
      change: '+3% so với tháng trước',
      changeType: 'increase',
      icon: Package,
      color: 'purple'
    },
    {
      title: 'Doanh thu',
      value: '₫125,430,000',
      change: '+15% so với tháng trước',
      changeType: 'increase',
      icon: DollarSign,
      color: 'yellow'
    }
  ];

  const recentOrders = [
    { id: '#001', customer: '<PERSON>uyễn Văn A', amount: '₫1,250,000', status: '<PERSON>àn thành', date: '2024-01-15' },
    { id: '#002', customer: 'Trần Thị B', amount: '₫850,000', status: 'Đang xử lý', date: '2024-01-15' },
    { id: '#003', customer: 'Lê Văn C', amount: '₫2,100,000', status: 'Đang giao', date: '2024-01-14' },
    { id: '#004', customer: 'Phạm Thị D', amount: '₫750,000', status: 'Hoàn thành', date: '2024-01-14' },
    { id: '#005', customer: 'Hoàng Văn E', amount: '₫1,500,000', status: 'Chờ xác nhận', date: '2024-01-13' },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Hoàn thành':
        return 'bg-green-100 text-green-800';
      case 'Đang xử lý':
        return 'bg-blue-100 text-blue-800';
      case 'Đang giao':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chờ xác nhận':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Tổng quan về hệ thống</p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts and tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Đơn hàng gần đây</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mã đơn
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Khách hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Số tiền
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.map((order) => (
                  <tr key={order.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {order.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {order.customer}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {order.amount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Thao tác nhanh</h3>
          </div>
          <div className="p-6 space-y-4">
            <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Package size={18} className="mr-2" />
              Thêm sản phẩm mới
            </button>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <Users size={18} className="mr-2" />
              Quản lý người dùng
            </button>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <TrendingUp size={18} className="mr-2" />
              Xem báo cáo
            </button>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
              <Activity size={18} className="mr-2" />
              Theo dõi hoạt động
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
